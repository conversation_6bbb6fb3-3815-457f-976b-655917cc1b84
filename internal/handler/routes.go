// Code generated by goctl. DO NOT EDIT.
// goctls v1.10.10

package handler

import (
	"net/http"

	payment "com.xiaojing.pay_service/internal/handler/payment"
	transfer "com.xiaojing.pay_service/internal/handler/transfer"
	useraccountflow "com.xiaojing.pay_service/internal/handler/useraccountflow"
	"com.xiaojing.pay_service/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/payment/paymentWxPayCallback",
				Handler: payment.PaymentWxPayCallbackHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/payment/paymentWxRefundCallback",
				Handler: payment.PaymentWxRefundCallbackHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/payment/autorefund",
				Handler: payment.AutoRefundHandler(serverCtx),
			},
		},
		rest.WithPrefix("/v1/api"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/payment/create",
				Handler: payment.PaymentHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/payment/account/recharge",
				Handler: payment.PaymentAccountHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/payment/refund",
				Handler: payment.RefundHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/payment/query/:paymentId",
				Handler: payment.QueryOrderStateHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/payment/refund/query/:refundId",
				Handler: payment.QueryRefundStateHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/payment/account/balance",
				Handler: payment.GetUserAccountHandler(serverCtx),
			},
		},
		rest.WithPrefix("/v1/api"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/tb_user_account_flow/create",
				Handler: useraccountflow.CreateTbUserAccountFlowHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/tb_user_account_flow/update",
				Handler: useraccountflow.UpdateTbUserAccountFlowHandler(serverCtx),
			},
			{
				Method:  http.MethodDelete,
				Path:    "/tb_user_account_flow/delete",
				Handler: useraccountflow.DeleteTbUserAccountFlowHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/payment/account/flow",
				Handler: useraccountflow.GetTbUserAccountFlowListHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/tb_user_account_flow",
				Handler: useraccountflow.GetTbUserAccountFlowByIdHandler(serverCtx),
			},
		},
		rest.WithPrefix("/v1/api"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/tb_payment_record/create",
				Handler: payment.CreateTbPaymentRecordHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/tb_payment_record/update",
				Handler: payment.UpdateTbPaymentRecordHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/tb_payment_record/delete",
				Handler: payment.DeleteTbPaymentRecordHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/tb_payment_record/list",
				Handler: payment.GetTbPaymentRecordListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/tb_payment_record",
				Handler: payment.GetTbPaymentRecordByIdHandler(serverCtx),
			},
		},
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/transfer",
				Handler: transfer.TransferHandler(serverCtx),
			},
		},
		rest.WithPrefix("/internal"),
	)
}
