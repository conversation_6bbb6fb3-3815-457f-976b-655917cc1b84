package transfer

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"com.xiaojing.pay_service/internal/logic/transfer"
	"com.xiaojing.pay_service/internal/svc"
	"com.xiaojing.pay_service/internal/types"
)

// swagger:route post /internal/transfer transfer Transfer
//

//

//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: TransferReq
//
// Responses:
//  200: TransferResp

func TransferHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.TransferReq
		if err := httpx.Parse(r, &req, true); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := transfer.NewTransfer(r.Context(), svcCtx)
		resp, err := l.Transfer(&req)
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
