// Code generated by goctl. DO NOT EDIT.
package types

// The basic response with data | 基础带数据信息
// swagger:model BaseDataInfo
type BaseDataInfo struct {
	// Error code | 错误代码
	Code int `json:"code"`
	// Message | 提示信息
	Msg string `json:"msg"`
	// Data | 数据
	Data string `json:"data,omitempty"`
}

// The basic response with data | 基础带数据信息
// swagger:model BaseListInfo
type BaseListInfo struct {
	// The total number of data | 数据总数
	Total uint64 `json:"total"`
	// Data | 数据
	Data string `json:"data,omitempty"`
}

// The basic response without data | 基础不带数据信息
// swagger:model BaseMsgResp
type BaseMsgResp struct {
	// Error code | 错误代码
	Code int `json:"code"`
	// Message | 提示信息
	Msg string `json:"msg"`
}

// The page request parameters | 列表请求参数
// swagger:model PageInfo
type PageInfo struct {
	// Page number | 第几页
	// required : true
	// min : 0
	Page uint64 `form:"page" validate:"required,number,gt=0"`
	// Page size | 单页数据行数
	// required : true
	// max : 100000
	PageSize uint64 `form:"pageSize" validate:"required,number,lt=100000"`
}

// Basic ID request | 基础ID参数请求
// swagger:model IDReq
type IDReq struct {
	// ID
	// Required: true
	Id uint64 `json:"id" validate:"number"`
}

// Basic IDs request | 基础ID数组参数请求
// swagger:model IDsReq
type IDsReq struct {
	// IDs
	// Required: true
	Ids []uint64 `json:"ids"`
}

// Basic ID request | 基础ID地址参数请求
// swagger:model IDPathReq
type IDPathReq struct {
	// ID
	// Required: true
	Id uint64 `path:"id"`
}

// Basic ID request (int32) | 基础ID参数请求 (int32)
// swagger:model IDInt32Req
type IDInt32Req struct {
	// ID
	// Required: true
	Id int32 `json:"id" validate:"number"`
}

// Basic IDs request (int32) | 基础ID数组参数请求 (int32)
// swagger:model IDsInt32Req
type IDsInt32Req struct {
	// IDs
	// Required: true
	Ids []int32 `json:"ids"`
}

// Basic ID request (int32) | 基础ID地址参数请求 (int32)
// swagger:model IDInt32PathReq
type IDInt32PathReq struct {
	// ID
	// Required: true
	Id int32 `path:"id"`
}

// Basic ID request (uint32) | 基础ID参数请求 (uint32)
// swagger:model IDUint32Req
type IDUint32Req struct {
	// ID
	// Required: true
	Id uint32 `json:"id" validate:"number"`
}

// Basic IDs request (uint32) | 基础ID数组参数请求 (uint32)
// swagger:model IDsUint32Req
type IDsUint32Req struct {
	// IDs
	// Required: true
	Ids []uint32 `json:"ids"`
}

// Basic ID request (uint32) | 基础ID地址参数请求 (uint32)
// swagger:model IDUint32PathReq
type IDUint32PathReq struct {
	// ID
	// Required: true
	Id uint32 `path:"id"`
}

// Basic ID request (int64) | 基础ID参数请求 (int64)
// swagger:model IDInt64Req
type IDInt64Req struct {
	// ID
	// Required: true
	Id int64 `json:"id" validate:"number"`
}

// Basic IDs request (int64) | 基础ID数组参数请求 (int64)
// swagger:model IDsInt64Req
type IDsInt64Req struct {
	// IDs
	// Required: true
	Ids []int64 `json:"ids"`
}

// Basic ID request (int64) | 基础ID地址参数请求 (int64)
// swagger:model IDInt64PathReq
type IDInt64PathReq struct {
	// ID
	// Required: true
	Id int64 `path:"id"`
}

// Basic ID request (string) | 基础ID参数请求 (string)
// swagger:model IDStringReq
type IDStringReq struct {
	// ID
	// Required: true
	Id string `json:"id"`
}

// Basic IDs request (string) | 基础ID数组参数请求 (string)
// swagger:model IDsStringReq
type IDsStringReq struct {
	// IDs
	// Required: true
	Ids []string `json:"ids"`
}

// Basic ID request (string) | 基础ID地址参数请求 (string)
// swagger:model IDStringPathReq
type IDStringPathReq struct {
	// ID
	// Required: true
	Id string `path:"id"`
}

// Basic UUID request in path | 基础UUID地址参数请求
// swagger:model UUIDPathReq
type UUIDPathReq struct {
	// ID
	// Required: true
	Id string `path:"id"`
}

// Basic UUID request | 基础UUID参数请求
// swagger:model UUIDReq
type UUIDReq struct {
	// ID
	// required : true
	// max length : 36
	// min length : 36
	Id string `json:"id" validate:"required,len=36"`
}

// Basic UUID array request | 基础UUID数组参数请求
// swagger:model UUIDsReq
type UUIDsReq struct {
	// Ids
	// Required: true
	Ids []string `json:"ids"`
}

// The base ID response data | 基础ID信息
// swagger:model BaseIDInfo
type BaseIDInfo struct {
	// ID
	Id *uint64 `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The base ID response data (int64) | 基础ID信息 (int64)
// swagger:model BaseIDInt64Info
type BaseIDInt64Info struct {
	// ID
	Id *int64 `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The base ID response data (int32) | 基础ID信息 (int32)
// swagger:model BaseIDInt32Info
type BaseIDInt32Info struct {
	// ID
	Id *int32 `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The base ID response data (uint32) | 基础ID信息 (uint32)
// swagger:model BaseIDUint32Info
type BaseIDUint32Info struct {
	// ID
	Id *uint32 `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The base UUID response data | 基础UUID信息
// swagger:model BaseUUIDInfo
type BaseUUIDInfo struct {
	// ID
	Id *string `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The base ID response data (string) | 基础ID信息 (string)
// swagger:model BaseIDStringInfo
type BaseIDStringInfo struct {
	// ID
	Id *string `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// swagger:model PaymentReq
type PaymentReq struct {
	OrderId string `json:"orderId"`
	// 订单类型 1-普通订单 2-会员购买 3-余额充值
	OrderType int `json:"orderType"`
	// 支付方式：1-微信支付，2-支付宝，3-余额支付
	PaymentMethod int `json:"paymentMethod"`
	// 支付金额
	Amount   float64 `json:"amount"`
	ClientIp string  `json:"clientIp,optional"`
	OpenId   string  `json:"openId"`
}

// swagger:model BasePaymentResp
type BasePaymentResp struct {
	BaseDataInfo
	Data WxResponseInfo `json:"data"`
}

// swagger:model WxResponseInfo
type WxResponseInfo struct {
	WxPaymentParams PaymentResp `json:"wxPaymentParams"`
	PaymentId       string      `json:"paymentId"`
	PaymentNo       string      `json:"paymentNo"`
}

// swagger:model PaymentResp
type PaymentResp struct {
	AppId     string `json:"appId"`
	NonceStr  string `json:"nonceStr"`
	PaySign   string `json:"paySign"`
	Package   string `json:"package"`
	Timestamp string `json:"timestamp"`
	SignType  string `json:"signType"`
}

// swagger:model PaymentWxPayCallbackReq
type PaymentWxPayCallbackReq struct {
}

// swagger:model PaymentWxPayCallbackResp
type PaymentWxPayCallbackResp struct {
	ReturnCode string `json:"returnCode"`
}

// swagger:model PaymentWxRefundCallbackReq
type PaymentWxRefundCallbackReq struct {
}

// swagger:model PaymentWxRefundCallbackResp
type PaymentWxRefundCallbackResp struct {
	ReturnCode string `json:"returnCode"`
}

// swagger:model RefundReq
type RefundReq struct {
	OrderId      *string `json:"orderId"`
	Reason       *string `json:"reason,optional"`
	RefundAmount *int64  `json:"refundAmount"`
}

// swagger:model AutoRefundReq
type AutoRefundReq struct {
	OrderId      *string `json:"orderId"`
	Reason       *string `json:"reason"`
	RefundAmount *int64  `json:"refundAmount"`
}

// swagger:model QueryOrderStateReq
type QueryOrderStateReq struct {
	OrderId *string `path:"orderId"`
}

// swagger:model OrderStateResp
type OrderStateResp struct {
	BaseDataInfo
	Data QueryOrderDataInfo `json:"data"`
}

// swagger:model QueryOrderDataInfo
type QueryOrderDataInfo struct {
	PaymentId     string `json:"paymentId"`
	PaymentNo     string `json:"paymentNo"`
	OrderId       string `json:"orderId"`
	OrderNo       string `json:"orderNo"`
	Amount        int64  `json:"amount"`
	Status        int8   `json:"status"`
	PaymentMethod int8   `json:"paymentMethod"`
	PaymentTime   string `json:"paymentTime"`
	TransactionId string `json:"transactionId"`
}

// swagger:model RefundOrderStateReq
type RefundOrderStateReq struct {
	RefundId *string `path:"refundId"`
}

// swagger:model RefundOrderStateResp
type RefundOrderStateResp struct {
	BaseDataInfo
	Data RefundDataInfo `json:"data"`
}

// swagger:model RefundDataInfo
type RefundDataInfo struct {
	RefundId     *string `json:"refundId"`
	RefundNo     *string `json:"refundNo"`
	PaymentId    *string `json:"paymentId"`
	PaymentNo    *string `json:"paymentNo"`
	OrderId      *string `json:"orderId"`
	OrderNo      *string `json:"orderNo"`
	RefundAmount *int64  `json:"refundAmount"`
	Status       int8    `json:"status"`
	RefundTime   string  `json:"refundTime"`
	RefundReason *string `json:"refundReason"`
}

// swagger:model RefundResp
type RefundResp struct {
	BaseDataInfo
	Data RefundInfo `json:"data"`
}

// swagger:model RefundInfo
type RefundInfo struct {
	RefundId     string `json:"refundId"`
	RefundNo     string `json:"refundNo"`
	RefundAmount int64  `json:"refundAmount"`
	Status       int8   `json:"status"`
}

// swagger:model AccountBalanceResp
type AccountBalanceResp struct {
	BaseDataInfo
	Data AccountInfo `json:"data"`
}

// swagger:model AccountInfo
type AccountInfo struct {
	Balance int64 `json:"balance"`
	Points  int64 `json:"points"`
}

// swagger:model AccountChargeReq
type AccountChargeReq struct {
	Amount        *int64  `json:"amount"`
	PaymentMethod *int8   `json:"paymentMethod"`
	ClientIp      *string `json:"clientIp"`
	DeviceInfo    *string `json:"deviceInfo"`
}

// The data of tb user account flow information | TbUserAccountFlow信息
// swagger:model TbUserAccountFlowInfo
type TbUserAccountFlowInfo struct {
	// 流水ID，主键
	FlowId *string `json:"flowId,optional"`
	// 用户ID
	UserId *string `json:"userId,optional"`
	// 流水号
	FlowNo *string `json:"flowNo,optional"`
	// 流水类型：1-充值，2-消费，3-退款，4-提现，5-调整
	FlowType *int8 `json:"flowType,optional"`
	// 金额
	Amount *float64 `json:"amount,optional"`
	// 变动后余额
	Balance *float64 `json:"balance,optional"`
	// 关联订单ID
	OrderId *string `json:"orderId,optional"`
	// 关联支付ID
	PaymentId *string `json:"paymentId,optional"`
	// 描述
	Description *string `json:"description,optional"`
	// 操作人ID
	OperatorId *string `json:"operatorId,optional"`
	// 操作人姓名
	OperatorName *string `json:"operatorName,optional"`
	// 流水时间
	FlowTime *int64 `json:"flowTime,optional"`
	// 备注
	Remark *string `json:"remark,optional"`
	// 创建时间
	CreateTime *int64 `json:"createTime,optional"`
	// 更新时间
	UpdateTime *int64 `json:"updateTime,optional"`
}

// The response data of tb user account flow list | TbUserAccountFlow信息列表数据
// swagger:model TbUserAccountFlowListResp
type TbUserAccountFlowListResp struct {
	BaseDataInfo
	// The tb user account flow list data | TbUserAccountFlow信息列表数据
	Data TbUserAccountFlowListInfo `json:"data"`
}

// The tb user account flow list data | TbUserAccountFlow信息列表数据
// swagger:model TbUserAccountFlowListInfo
type TbUserAccountFlowListInfo struct {
	BaseListInfo
	// The tb user account flow list data | TbUserAccountFlow信息列表数据
	Data []TbUserAccountFlowInfo `json:"data"`
}

// Get tb user account flow list request params | TbUserAccountFlow信息列表请求参数
// swagger:model TbUserAccountFlowListReq
type TbUserAccountFlowListReq struct {
	PageInfo
	// 流水类型：1-充值，2-消费，3-退款，4-提现，5-调整
	FlowType  *int8   `form:"flowType,optional"`
	StartDate *string `form:"startDate,optional"`
	EndDate   *string `form:"endDate,optional"`
}

// The tb user account flow information response | TbUserAccountFlow信息返回体
// swagger:model TbUserAccountFlowInfoResp
type TbUserAccountFlowInfoResp struct {
	BaseDataInfo
	// tb user account flow information | TbUserAccountFlow信息数据
	Data TbUserAccountFlowInfo `json:"data"`
}

// swagger:model DeleteTbUserAccountFlowIDsReq
type DeleteTbUserAccountFlowIDsReq struct {
	FlowIds []string `json:"flowIds"`
}

// swagger:model GetTbUserAccountFlowByIdReq
type GetTbUserAccountFlowByIdReq struct {
	FlowId string `json:"flowId"`
}

// The data of tb payment record information | TbPaymentRecord信息
// swagger:model TbPaymentRecordInfo
type TbPaymentRecordInfo struct {
	// 支付ID，主键
	PaymentId *string `json:"paymentId,optional"`
	// 订单ID
	OrderId *string `json:"orderId,optional"`
	// 订单类型：1-普通订单，2-会员购买，3-余额充值
	OrderType *int8 `json:"orderType,optional"`
	// 用户ID
	UserId *string `json:"userId,optional"`
	// 商户ID
	MerchantId *string `json:"merchantId,optional"`
	// 支付单号
	PaymentNo *string `json:"paymentNo,optional"`
	// 支付方式：1-微信支付，2-支付宝，3-余额支付
	PaymentMethod *int8 `json:"paymentMethod,optional"`
	// 支付渠道
	PaymentChannel *string `json:"paymentChannel,optional"`
	// 支付金额
	Amount *float64 `json:"amount,optional"`
	// 支付状态：0-待支付，1-支付中，2-支付成功，3-支付失败，4-已退款
	Status *int8 `json:"status,optional"`
	// 第三方交易号
	TransactionId *string `json:"transactionId,optional"`
	// 支付时间
	PaymentTime *int64 `json:"paymentTime,optional"`
	// 完成时间
	FinishTime *int64 `json:"finishTime,optional"`
	// 客户端IP
	ClientIp *string `json:"clientIp,optional"`
	// 设备信息
	DeviceInfo *string `json:"deviceInfo,optional"`
	// 退款ID
	RefundId *string `json:"refundId,optional"`
	// 退款金额
	RefundAmount *float64 `json:"refundAmount,optional"`
	// 退款时间
	RefundTime *int64 `json:"refundTime,optional"`
	// 失败原因
	FailureReason *string `json:"failureReason,optional"`
	// 备注
	Remark *string `json:"remark,optional"`
	// 创建时间
	CreateTime *int64 `json:"createTime,optional"`
	// 更新时间
	UpdateTime *int64 `json:"updateTime,optional"`
}

// The response data of tb payment record list | TbPaymentRecord信息列表数据
// swagger:model TbPaymentRecordListResp
type TbPaymentRecordListResp struct {
	BaseDataInfo
	// The tb payment record list data | TbPaymentRecord信息列表数据
	Data TbPaymentRecordListInfo `json:"data"`
}

// The tb payment record list data | TbPaymentRecord信息列表数据
// swagger:model TbPaymentRecordListInfo
type TbPaymentRecordListInfo struct {
	BaseListInfo
	// The tb payment record list data | TbPaymentRecord信息列表数据
	Data []TbPaymentRecordInfo `json:"data"`
}

// Get tb payment record list request params | TbPaymentRecord信息列表请求参数
// swagger:model TbPaymentRecordListReq
type TbPaymentRecordListReq struct {
	PageInfo
	// 支付ID，主键
	PaymentId *string `json:"paymentId,optional"`
	// 订单ID
	OrderId *string `json:"orderId,optional"`
	// 用户ID
	UserId *string `json:"userId,optional"`
	// 商户ID
	MerchantId *string `json:"merchantId,optional"`
	// 支付单号
	PaymentNo *string `json:"paymentNo,optional"`
	// 支付方式：1-微信支付，2-支付宝，3-余额支付
	PaymentMethod *int8 `json:"paymentMethod,optional"`
	// 支付渠道
	PaymentChannel *string `json:"paymentChannel,optional"`
	// 支付金额
	Amount *float64 `json:"amount,optional"`
	// 支付状态：0-待支付，1-支付中，2-支付成功，3-支付失败，4-已退款
	Status *int8 `json:"status,optional"`
	// 第三方交易号
	TransactionId *string `json:"transactionId,optional"`
	// 支付时间
	PaymentTime *int64 `json:"paymentTime,optional"`
	// 完成时间
	FinishTime *int64 `json:"finishTime,optional"`
	// 客户端IP
	ClientIp *string `json:"clientIp,optional"`
	// 设备信息
	DeviceInfo *string `json:"deviceInfo,optional"`
	// 退款ID
	RefundId *string `json:"refundId,optional"`
	// 退款金额
	RefundAmount *float64 `json:"refundAmount,optional"`
	// 退款时间
	RefundTime *int64 `json:"refundTime,optional"`
	// 失败原因
	FailureReason *string `json:"failureReason,optional"`
	// 备注
	Remark *string `json:"remark,optional"`
	// 创建时间
	CreateTime *int64 `json:"createTime,optional"`
	// 更新时间
	UpdateTime *int64 `json:"updateTime,optional"`
}

// The tb payment record information response | TbPaymentRecord信息返回体
// swagger:model TbPaymentRecordInfoResp
type TbPaymentRecordInfoResp struct {
	BaseDataInfo
	// tb payment record information | TbPaymentRecord信息数据
	Data TbPaymentRecordInfo `json:"data"`
}

// swagger:model DeleteTbPaymentRecordIDsReq
type DeleteTbPaymentRecordIDsReq struct {
	PaymentIds []string `json:"paymentIds"`
}

// swagger:model GetTbPaymentRecordIDReq
type GetTbPaymentRecordIDReq struct {
	PaymentId string `json:"paymentId"`
}

// swagger:model TransferReq
type TransferReq struct {
	// 转账单号
	TransferNo string `json:"transferNo"`
	// 转账金额
	TransferAmount int64 `json:"transferAmount"`
	// 转账备注
	TransferRemark string `json:"transferRemark"`
	// 收款用户openid
	Openid string `json:"openid"`
	// 收款用户姓名
	UserName string `json:"userName"`
	// 转账渠道 1-微信 2-支付宝 3-银行
	TransferChannel int `json:"transferChannel"`
}

// swagger:model TransferResp
type TransferResp struct {
	BaseDataInfo
}
