package types

type UpdateOrderStatus struct {
	MsgID         string `json:"msgId"`
	OrderId       string `json:"orderId"`
	OrderType     int8   `json:"orderType"`
	PayStatus     int8   `json:"payStatus"`
	PaymentTime   int64  `json:"paymentTime"`
	TransactionId string `json:"transactionId"`
	Amount        int64  `json:"amount"`
}

// 微信商家转账请求参数
type WxTransferReq struct {
	// 商户系统内部的商家批次单号，要求此参数只能由数字、大小写字母组成，在商户系统内部唯一
	OutBatchNo string `json:"out_batch_no"`
	// 该笔批量转账的名称
	BatchName string `json:"batch_name"`
	// 转账说明，UTF8编码，最多允许32个字符
	BatchRemark string `json:"batch_remark"`
	// 转账金额单位为“分”。转账总金额必须与批次内所有明细转账金额之和保持一致，否则无法发起转账操作
	TotalAmount int64 `json:"total_amount"`
	// 一个转账批次单最多发起一千笔转账。转账总笔数必须与批次内所有明细之和保持一致，否则无法发起转账操作
	TotalNum int64 `json:"total_num"`
	// 发起批量转账的明细列表，最多一千笔
	// 转账接收方
	TransferDetailList []WxTransferDetail `json:"transferDetailList"`
	// 该批次转账使用的转账场景，如不填写则使用商家的默认场景，如无默认场景可为空，可前往“商家转账到零钱-前往功能”中申请。 如：1001-现金营销
	TransferSceneId string `json:"transfer_scene_id,omitempty"`
	// 商户后台接收转账结果更新通知的回调地址，通知URL必须为直接可访问的URL，不允许携带查询串，要求必须为https地址
	NotifyUrl string `json:"notify_url,omitempty"`
}

// 微信转账接收方
type WxTransferDetail struct {
	// 商户系统内部区分转账批次单下不同转账明细单的唯一标识，要求此参数只能由数字、大小写字母组成
	OutDetailNo string `json:"out_detail_no"`
	// 转账金额单位为“分”
	TransferAmount int64 `json:"transfer_amount"`
	// 单条转账备注（微信用户会收到该备注），UTF8编码，最多允许32个字符
	TransferRemark string `json:"transfer_remark"`
	// 商户appid下，某用户的openid
	Openid string `json:"openid"`
	// 收款方真实姓名。支持标准RSA算法和国密算法，公钥由微信侧提供 明细转账金额<0.3元时，不允许填写收款用户姓名 明细转账金额 >= 2,000元时，该笔明细必须填写收款用户姓名 同一批次转账明细中的姓名字段传入规则需保持一致，也即全部填写、或全部不填写 若商户传入收款用户姓名，微信支付会校验用户openID与姓名是否一致，并提供电子回单
	UserName string `json:"user_name,omitempty" encryption:"EM_APIV3"`
}

// 通用转账请求
type TransferReq struct {
	// 转账渠道：1-微信，2-支付宝，3-银行
	TransferChannel int `json:"transfer_channel"`
	// 微信转账参数，当 TransferChannel=1 时必填
	WxTransfer *WxTransferReq `json:"wx_transfer,omitempty"`
	// 支付宝转账参数，当 TransferChannel=2 时必填
	AlipayTransfer *AlipayTransferReq `json:"alipay_transfer,omitempty"`
	// 银行转账参数，当 TransferChannel=3 时必填
	BankTransfer *BankTransferReq `json:"bank_transfer,omitempty"`
}

// 支付宝转账请求参数
type AlipayTransferReq struct {
	// TODO: 添加支付宝转账所需参数
	OutBatchNo string `json:"out_batch_no"`
	BatchName  string `json:"batch_name"`
	// 其他支付宝特有参数
}

// 银行转账请求参数
type BankTransferReq struct {
	// TODO: 添加银行转账所需参数
	OutBatchNo string `json:"out_batch_no"`
	BatchName  string `json:"batch_name"`
	// 其他银行特有参数
}

// 微信批量转账响应
type TransferBatchResp struct {
	BaseDataInfo
	Data TransferBatchInfo `json:"data"`
}

// 微信批量转账信息
type TransferBatchInfo struct {
	// 商户系统内部的商家批次单号
	OutBatchNo string `json:"out_batch_no"`
	// 批次单号（微信/支付宝/银行返回的）
	BatchId string `json:"batch_id"`
	// 批次创建时间
	CreateTime string `json:"create_time"`
	// 批次状态
	BatchStatus string `json:"batch_status"`
	// 转账渠道：1-微信，2-支付宝，3-银行
	Channel int `json:"channel"`
}
