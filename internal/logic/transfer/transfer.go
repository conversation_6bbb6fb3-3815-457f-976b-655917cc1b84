package transfer

import (
	"com.xiaojing.pay_service/internal/constants"
	"com.xiaojing.pay_service/internal/svc"
	"com.xiaojing.pay_service/internal/types"
	"com.xiaojing.pay_service/pkg/xerr"
	"context"
	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/services/transferbatch"
	"github.com/zeromicro/go-zero/core/logx"
	"time"
)

// TransferLogic 转账逻辑处理
type TransferLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

// NewTransferLogic 创建转账逻辑处理器
func NewTransferLogic(ctx context.Context, svcCtx *svc.ServiceContext) *TransferLogic {
	return &TransferLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// TransferBatch 批量转账方法，根据不同的转账渠道调用不同的实现
func (l *TransferLogic) TransferBatch(req *types.TransferReq) (*types.TransferBatchResp, error) {
	// 根据转账渠道选择不同的处理方式
	switch req.TransferChannel {
	case constants.TransferChannelWechat:
		return l.wechatTransferBatch(req)
	case constants.TransferChannelAlipay:
		return l.alipayTransferBatch(req)
	case constants.TransferChannelBank:
		return l.bankTransferBatch(req)
	default:
		return nil, xerr.NewErrMsg("不支持的转账渠道")
	}
}

// wechatTransferBatch 微信批量转账实现
func (l *TransferLogic) wechatTransferBatch(req *types.TransferReq) (*types.TransferBatchResp, error) {
	// 转换为微信转账请求
	wxReq := req.WxTransfer
	if wxReq == nil {
		return nil, xerr.NewErrMsg("微信转账参数不能为空")
	}

	// 创建微信转账服务
	transferBatchApiService := transferbatch.TransferBatchApiService{Client: l.svcCtx.WxPayClient}

	// 构建转账明细列表
	transferDetails := make([]transferbatch.TransferDetailInput, 0, len(wxReq.TransferDetailList))
	for _, detail := range wxReq.TransferDetailList {
		transferDetail := transferbatch.TransferDetailInput{
			OutDetailNo:    core.String(detail.OutDetailNo),
			TransferAmount: core.Int64(detail.TransferAmount),
			TransferRemark: core.String(detail.TransferRemark),
			Openid:         core.String(detail.Openid),
		}
		// 如果有用户姓名，则添加
		if detail.UserName != "" {
			transferDetail.UserName = core.String(detail.UserName)
		}
		transferDetails = append(transferDetails, transferDetail)
	}

	// 构建批量转账请求
	batchTransferRequest := transferbatch.InitiateBatchTransferRequest{
		Appid:              core.String(l.svcCtx.Config.WxMiniConf.AppId),
		OutBatchNo:         core.String(wxReq.OutBatchNo),
		BatchName:          core.String(wxReq.BatchName),
		BatchRemark:        core.String(wxReq.BatchRemark),
		TotalAmount:        core.Int64(wxReq.TotalAmount),
		TotalNum:           core.Int64(wxReq.TotalNum),
		TransferDetailList: transferDetails,
	}

	// 添加可选参数
	if wxReq.TransferSceneId != "" {
		batchTransferRequest.TransferSceneId = core.String(wxReq.TransferSceneId)
	}
	if wxReq.NotifyUrl != "" {
		batchTransferRequest.NotifyUrl = core.String(wxReq.NotifyUrl)
	}

	// 发起批量转账请求
	resp, result, err := transferBatchApiService.InitiateBatchTransfer(l.ctx, batchTransferRequest)
	if err != nil {
		l.Errorf("微信批量转账失败: %v", err)
		return nil, xerr.NewErrMsg("微信批量转账失败: " + err.Error())
	}

	// 检查结果
	if result.Response.StatusCode != 200 {
		l.Errorf("微信批量转账请求失败，状态码: %d", result.Response.StatusCode)
		return nil, xerr.NewErrMsg("微信批量转账请求失败")
	}

	// 构建返回结果
	return &types.TransferBatchResp{
		BaseDataInfo: types.BaseDataInfo{
			Msg: "批量转账请求成功",
		},
		Data: types.TransferBatchInfo{
			OutBatchNo:  *resp.OutBatchNo,
			BatchId:     *resp.BatchId,
			CreateTime:  resp.CreateTime.Format(time.DateTime),
			BatchStatus: *resp.BatchStatus,
			Channel:     constants.TransferChannelWechat,
		},
	}, nil
}

// alipayTransferBatch 支付宝批量转账实现
func (l *TransferLogic) alipayTransferBatch(req *types.TransferReq) (*types.TransferBatchResp, error) {
	// TODO: 实现支付宝批量转账
	l.Infof("支付宝批量转账功能待实现")
	return nil, xerr.NewErrMsg("支付宝批量转账功能待实现")
}

// bankTransferBatch 银行批量转账实现
func (l *TransferLogic) bankTransferBatch(req *types.TransferReq) (*types.TransferBatchResp, error) {
	// TODO: 实现银行批量转账
	l.Infof("银行批量转账功能待实现")
	return nil, xerr.NewErrMsg("银行批量转账功能待实现")
}
