package transfer

import (
	"context"

	"com.xiaojing.pay_service/internal/svc"
	"com.xiaojing.pay_service/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type TransferLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewTransferLogic(ctx context.Context, svcCtx *svc.ServiceContext) *TransferLogic {
	return &TransferLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *TransferLogic) Transfer(req *types.TransferReq) (resp *types.TransferResp, err error) {
	// todo: add your logic here and delete this line

	return
}
