package constants

const (
	//微信支付
	ThirdPaymentPayTypeWechatPay = iota + 1
)

// 订单支付状态
const (
	ThirdOrderPaymentStatusPayWait       = iota //待支付
	ThirdOrderPaymentStatusPayProcessing        //支付中
	ThirdOrderPaymentStatusPaySuccess           //支付成功
	ThirdOrderPaymentStatusPayFailed            //支付失败
	ThirdOrderPaymentStatusRefundSuccess        //已退款
	//ThirdOrderPaymentStatusRefundAbnormal              //退款异常
	//ThirdOrderPaymentStatusRefundProcessing            //退款处理中
)

// 会员支付状态
const (
	ThirdMemberPaymentStatusPayWait    = iota + 1 //待支付
	ThirdMemberPaymentStatusPaySuccess            //已支付
	ThirdMemberPaymentStatusPayFailed             //支付失败
)

// 余额充值支付状态
const (
	ThirdRechargePaymentStatusPayWait    = iota + 1 //待支付
	ThirdRechargePaymentStatusPaySuccess            //已支付
	ThirdRechargePaymentStatusPayFailed             //支付失败
)

// 订单类型
const (
	PaymentTypeOrder      = iota + 1 // 普通订单
	PaymentTypeMembership            // 会员购买
	PaymentTypeRecharge              // 余额充值
)

const (
	AttachRecharge   = "recharge"
	AttachMembership = "membership"
)

const (
	SUCCESS    = "SUCCESS"    //支付成功
	REFUND     = "REFUND"     //转入退款
	NOTPAY     = "NOTPAY"     //未支付
	CLOSED     = "CLOSED"     //已关闭
	REVOKED    = "REVOKED"    //已撤销（付款码支付）
	USERPAYING = "USERPAYING" //用户支付中（付款码支付）
	PAYERROR   = "PAYERROR"   //支付失败(其他原因，如银行返回失败)
	ABNORMAL   = "ABNORMAL"   //退款异常
	PROCESSING = "PROCESSING" //退款中
)

const (
	Layout = "2006-01-02"
)

// 转账渠道
const (
	TransferChannelWechat = iota + 1 // 微信转账
	TransferChannelAlipay            // 支付宝转账
	TransferChannelBank              // 银行转账
)
