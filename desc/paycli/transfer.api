import "../base.api"

type {
    TransferReq {
        // 转账单号
        TransferNo string `json:"transferNo"`
        // 转账金额
        TransferAmount int64 `json:"transferAmount"`
        // 转账备注
        TransferRemark string `json:"transferRemark"`
        // 收款用户openid
        Openid string `json:"openid"`
        // 收款用户姓名
        UserName string `json:"userName"`
        // 转账渠道 1-微信 2-支付宝 3-银行
        TransferChannel int `json:"transferChannel"`
    }

    TransferResp {
        BaseDataInfo
    }


}

@server(
    prefix: /internal
    group: transfer
)

service Paycli {
    @handler transfer
    post /transfer (TransferReq) returns (TransferResp)
}
