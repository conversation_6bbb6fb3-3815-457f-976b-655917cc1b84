package main

import (
	"fmt"
	"log"
)

// 模拟常量
const (
	PaymentTypeOrder      = 1 // 普通订单
	PaymentTypeMembership = 2 // 会员购买
	PaymentTypeRecharge   = 3 // 余额充值
)

const (
	TransferChannelWechat = 1 // 微信转账
	TransferChannelAlipay = 2 // 支付宝转账
	TransferChannelBank   = 3 // 银行转账
)

// 模拟TransferReq结构
type TransferReq struct {
	TransferNo      string `json:"transferNo"`
	TransferAmount  int64  `json:"transferAmount"`
	TransferRemark  string `json:"transferRemark"`
	Openid          string `json:"openid"`
	UserName        string `json:"userName"`
	TransferChannel int    `json:"transferChannel"`
	OrderType       int    `json:"orderType,optional"`
}

// 验证转账请求参数
func validateTransferRequest(req *TransferReq) error {
	if req.TransferNo == "" {
		return fmt.Errorf("转账单号不能为空")
	}
	if req.TransferAmount <= 0 {
		return fmt.Errorf("转账金额必须大于0")
	}
	if req.Openid == "" {
		return fmt.Errorf("收款用户openid不能为空")
	}
	if req.TransferChannel < TransferChannelWechat || req.TransferChannel > TransferChannelBank {
		return fmt.Errorf("转账渠道参数无效")
	}
	return nil
}

// 根据转账类型处理后续逻辑
func handleTransferByType(req *TransferReq, userId, transferId string) error {
	switch req.OrderType {
	case PaymentTypeOrder:
		fmt.Printf("处理普通订单转账，用户ID: %s, 转账ID: %s\n", userId, transferId)
		return handleNormalOrderTransfer(req, userId, transferId)
		
	case PaymentTypeMembership:
		fmt.Printf("处理会员购买转账，用户ID: %s, 转账ID: %s\n", userId, transferId)
		return handleMembershipTransfer(req, userId, transferId)
		
	case PaymentTypeRecharge:
		fmt.Printf("处理余额充值转账，用户ID: %s, 转账ID: %s\n", userId, transferId)
		return handleRechargeTransfer(req, userId, transferId)
		
	default:
		fmt.Printf("未指定转账类型，按普通转账处理，用户ID: %s, 转账ID: %s\n", userId, transferId)
		return handleNormalOrderTransfer(req, userId, transferId)
	}
}

func handleNormalOrderTransfer(req *TransferReq, userId, transferId string) error {
	fmt.Printf("普通订单转账处理完成，转账单号: %s\n", req.TransferNo)
	return nil
}

func handleMembershipTransfer(req *TransferReq, userId, transferId string) error {
	fmt.Printf("会员购买转账处理完成，转账单号: %s，需要更新用户会员状态\n", req.TransferNo)
	return nil
}

func handleRechargeTransfer(req *TransferReq, userId, transferId string) error {
	fmt.Printf("余额充值转账处理完成，转账单号: %s，需要更新用户余额: +%d\n", req.TransferNo, req.TransferAmount)
	return nil
}

func main() {
	fmt.Println("=== 转账功能测试 ===")
	
	// 测试用例1：普通订单转账
	req1 := &TransferReq{
		TransferNo:      "T001",
		TransferAmount:  10000, // 100元
		TransferRemark:  "普通订单转账",
		Openid:          "test_openid_1",
		UserName:        "张三",
		TransferChannel: TransferChannelWechat,
		OrderType:       PaymentTypeOrder,
	}
	
	fmt.Println("\n--- 测试1：普通订单转账 ---")
	if err := validateTransferRequest(req1); err != nil {
		log.Printf("参数验证失败: %v", err)
	} else {
		handleTransferByType(req1, "user123", "transfer123")
	}
	
	// 测试用例2：会员购买转账
	req2 := &TransferReq{
		TransferNo:      "T002",
		TransferAmount:  50000, // 500元
		TransferRemark:  "会员购买转账",
		Openid:          "test_openid_2",
		UserName:        "李四",
		TransferChannel: TransferChannelWechat,
		OrderType:       PaymentTypeMembership,
	}
	
	fmt.Println("\n--- 测试2：会员购买转账 ---")
	if err := validateTransferRequest(req2); err != nil {
		log.Printf("参数验证失败: %v", err)
	} else {
		handleTransferByType(req2, "user456", "transfer456")
	}
	
	// 测试用例3：余额充值转账
	req3 := &TransferReq{
		TransferNo:      "T003",
		TransferAmount:  20000, // 200元
		TransferRemark:  "余额充值转账",
		Openid:          "test_openid_3",
		UserName:        "王五",
		TransferChannel: TransferChannelWechat,
		OrderType:       PaymentTypeRecharge,
	}
	
	fmt.Println("\n--- 测试3：余额充值转账 ---")
	if err := validateTransferRequest(req3); err != nil {
		log.Printf("参数验证失败: %v", err)
	} else {
		handleTransferByType(req3, "user789", "transfer789")
	}
	
	// 测试用例4：参数验证失败
	req4 := &TransferReq{
		TransferNo:      "", // 空转账单号
		TransferAmount:  10000,
		TransferRemark:  "测试失败",
		Openid:          "test_openid_4",
		UserName:        "赵六",
		TransferChannel: TransferChannelWechat,
		OrderType:       PaymentTypeOrder,
	}
	
	fmt.Println("\n--- 测试4：参数验证失败 ---")
	if err := validateTransferRequest(req4); err != nil {
		fmt.Printf("参数验证失败（预期）: %v\n", err)
	} else {
		handleTransferByType(req4, "user000", "transfer000")
	}
	
	fmt.Println("\n=== 测试完成 ===")
}
